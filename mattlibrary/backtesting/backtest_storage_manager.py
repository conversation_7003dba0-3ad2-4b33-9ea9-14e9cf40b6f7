"""Backtest storage manager module."""

import logging
import datetime as dt
import os
import uuid
import importlib
import inspect
import shutil
from typing import List
from types import ModuleType
import polars as pl
from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration
from mattlibrary.trading.strategy_base import StrategyBase

class BacktestStorageManager:
    """Class for managing storage related to backtests."""
    
    def __init__(self, base_directory: str):
        """Initialize the storage manager.
        
        Args:
            base_directory: Base directory for storing backtests
        """
        # Initialize logger
        self.logger = logging.getLogger(__name__)
        
        # Store base directory
        self.base_directory = base_directory
                
        #create backtests directory if it does not yet exist
        if not os.path.exists(self.base_directory):
            os.makedirs(self.base_directory)

        #scan all subdirectories directly under base_directory and only delete the entire subdirectory if there is not a single file stored either in the subdirectory itself or other inner subdirectories
        for root, dirs, files in os.walk(self.base_directory, topdown=False):
            for name in dirs:
                dir_path = os.path.join(root, name)
                if not os.listdir(dir_path):
                    os.rmdir(dir_path)
                
        # Log initialization
        self.logger.info(f"BacktestStorageManager initialized with base directory: {self.base_directory}")
        

    def store_backtest_metadata(self, configuration: BacktestConfiguration, strategies: List[StrategyBase], strategy_generator_module: ModuleType, data_generator_module: ModuleType):
        """Create a new backtest directory.
        
        Args:
            configuration: Configuration for the backtest
        """
        backtest_name = f"backtest_{dt.datetime.now().strftime('%Y-%b-%d %H-%M-%S')}"
        backtest_directory = os.path.join(self.base_directory, backtest_name)
     
        #create directory for this backtest, and subdirectories
        os.makedirs(backtest_directory)
        
        #store configuration
        with open(os.path.join(backtest_directory, "configuration.json"), "w") as f:
            f.write(configuration.to_json())

        #store strategy generation code
        strategy_module = inspect.getmodule(strategy_generator_module)
        strategy_path_filename = inspect.getfile(strategy_module)
        shutil.copy(strategy_path_filename, os.path.join(backtest_directory, "strategy_generation_code.py"))

        #store data generation code
        data_module = inspect.getmodule(data_generator_module)
        data_path_filename = inspect.getfile(data_module)
        shutil.copy(data_path_filename, os.path.join(backtest_directory, "data_generation_code.py"))
        
        #store the actual code of the strategies, used
        strategy_dir = os.path.join(backtest_directory, "strategies")
        os.makedirs(strategy_dir)
        
        unique_strategy_types = set([type(x) for x in strategies])
        for strategy_type in unique_strategy_types:
            strategy_path_filename = inspect.getfile(strategy_type)
            strategy_name = os.path.basename(strategy_path_filename)
            shutil.copy(strategy_path_filename, os.path.join(strategy_dir, strategy_name))


    def load_backtest_modules(self, backtest_path: str) -> tuple[BacktestConfiguration, List[StrategyBase], pl.DataFrame]:
        """Load a backtest configuration from a file.
        
        Args:
            file_name: Name of the file to load
        """
        #load configuration
        configuration_path_filename = os.path.join(backtest_path, "configuration.json")
        with open(configuration_path_filename, "r") as f:
            configuration = BacktestConfiguration.from_json(f.read())
        
        #load strategy generation code
        strategy_path_filename = os.path.join(backtest_path, "strategy_generation_code.py")
        spec = importlib.util.spec_from_file_location("strategy_generation_code", strategy_path_filename)
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        generate_strategies = strategy_module.generate_strategies
        strategies = generate_strategies(configuration)

        #load data generation code
        data_path_filename = os.path.join(backtest_path, "data_generation_code.py")
        spec = importlib.util.spec_from_file_location("data_generation_code", data_path_filename)
        data_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(data_module)
        generate_data = data_module.generate_data
        source_data = generate_data(configuration)

        return configuration, strategies, source_data