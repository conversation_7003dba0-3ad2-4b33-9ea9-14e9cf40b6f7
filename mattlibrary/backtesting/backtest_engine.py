"""Backtest engine module for running trading strategies on historical data."""

import logging
from typing import List
import polars as pl
from mattlibrary.trading.strategy_base import StrategyBase
from mattlibrary.trading.trading_engine import TradingEngine
from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration
from mattlibrary.logging import logging_config


class BacktestEngine:
    """Engine for running backtests on trading strategies with historical data."""
    

    def __init__(self, configuration: BacktestConfiguration, strategies: List[StrategyBase], data_source: pl.DataFrame):
        
        # Store configuration and generate unique backtest ID
        self.configuration = configuration
        self.strategies = strategies
        self.data_source = data_source
        
        # Configure logging
        logging_config.setup_logging(
            logging_enabled=self.configuration.logging_enabled, 
            log_level=logging.DEBUG, 
            log_to_file=self.configuration.log_to_file,
            log_file=self.configuration.log_file, 
            log_to_console=self.configuration.log_to_console, 
            clean_log_file=True)

        # Initialize logger        
        self.logger = logging.getLogger(__name__)
        
        #validate data source
        if not isinstance(self.data_source, pl.DataFrame):
            self.logger.error(f"Invalid data source type: {type(self.data_source)}. Expected Polars DataFrame")
            raise TypeError("Data source must be a Polars DataFrame")
            
        if len(self.data_source) == 0:
            self.logger.error("Empty data source provided")
            raise ValueError("Data source contains no rows")

        #validate strategies
        if len(self.strategies) == 0:
            self.logger.error("No strategies provided")
            raise ValueError("At least one strategy must be provided")
        
        for strategy in self.strategies:
            if not isinstance(strategy, StrategyBase):
                self.logger.error(f"Invalid strategy type: {type(strategy)}. Expected StrategyBase object")
                raise TypeError("Strategies must be a list of StrategyBase objects")

        # Initialize the trading engine
        self.trading_engine = TradingEngine(self.configuration.starting_balance, self.configuration.base_currency, self.configuration.track_performance, self.configuration.execution_plugin_type)
        
        #add strategies to trading engine
        for strategy in self.strategies:
            self.trading_engine.add_strategy(strategy)

        # Log initialization
        self.logger.info(f"Initialized BacktestEngine [balance={self.configuration.starting_balance} {self.configuration.base_currency}, track_performance={self.configuration.track_performance}]")


    def start_backtest(self):
        """Start the backtest."""
        row_count = len(self.data_source)
        strategy_count = len(self.trading_engine.strategies)

        self.logger.info(f"Starting data iteration [rows={row_count}, active_strategies={strategy_count}]")
        
        try:
            for row in self.data_source.iter_rows(named=True):
                # Process each row through the trading engine
                self.trading_engine.process_market_data(row)
            self.logger.info(f"Data iteration complete [processed_rows={row_count}, total_strategies={strategy_count}]")
        except Exception as e:
            self.logger.error(f"Error during data iteration: {str(e)}")
            raise


    def finalize(self):
        """Finalize the backtest."""
        try:
            # Delegate to trading engine
            self.trading_engine.finalize()
            self.logger.info("Backtest finalized")
        except Exception as e:
            self.logger.error(f"Error during backtest finalization: {str(e)}")
            raise


    def get_performance_data(self) -> dict:
        """Get the performance data of the backtest from the trading engine"""
        return self.trading_engine.get_performance_data()
        
