"""Backtest configuration module."""

import datetime
import json


class BacktestConfiguration:
    """Configuration class for backtest parameters."""
    
    def __init__(self, 
                 starting_balance: float,
                 base_currency: str,
                 symbols_data: list,
                 symbols_traded: list,
                 data_directory: str,
                 dataset_name: str,
                 start_dt: datetime.datetime,
                 end_dt: datetime.datetime,
                 sma_window_size: int,
                 order_size: float,
                 is_target_size: bool,
                 execution_plugin_type: str,
                 enable_strategy_logging: bool,
                 logging_enabled: bool,
                 log_to_console: bool,
                 log_to_file: bool,
                 log_file: str,
                 track_performance: bool,
                 excel_logging: bool,
                 excel_output_dir: str,
                 visualize_performance: bool):
        
        # Backtest parameters
        self.starting_balance = starting_balance
        self.base_currency = base_currency
        self.symbols_data = symbols_data
        self.symbols_traded = symbols_traded
        self.execution_plugin_type = execution_plugin_type
        
        # Data parameters
        self.data_directory = data_directory
        self.dataset_name = dataset_name
        self.start_dt = start_dt
        self.end_dt = end_dt
        
        # Strategy parameters
        self.sma_window_size = sma_window_size
        self.order_size = order_size
        self.is_target_size = is_target_size
        self.enable_strategy_logging = enable_strategy_logging
        
        # Logging
        self.logging_enabled = logging_enabled
        self.log_to_console = log_to_console
        self.log_to_file = log_to_file
        self.log_file = log_file
        
        # Performance tracking
        self.track_performance = track_performance
        self.excel_logging = excel_logging
        self.excel_output_dir = excel_output_dir
        self.visualize_performance = visualize_performance


    def to_json(self, indent: int = 4) -> str:
        """
        Serializes the BacktestConfiguration instance to a JSON string.
        Handles datetime objects by converting them to ISO 8601 strings.

        Args:
            indent: The indentation level for the JSON output.

        Returns:
            A JSON string representation of the configuration.
        """
        # Create a dictionary from the instance's attributes
        config_dict = self.__dict__.copy()
        
        # Convert datetime objects to ISO 8601 strings
        for key, value in config_dict.items():
            if isinstance(value, datetime.datetime):
                config_dict[key] = value.isoformat()
        
        # Return the JSON string
        return json.dumps(config_dict, indent=indent)


    @classmethod
    def from_json(cls, json_string: str):
        """
        Deserializes a JSON string back into a BacktestConfiguration instance.
        Handles ISO 8601 datetime strings by converting them back to datetime objects.

        Args:
            json_string: A JSON string representing the configuration.

        Returns:
            An instance of BacktestConfiguration.
        """
        def datetime_parser(dct):
            """
            Custom object hook for json.loads() to convert datetime strings.
            """
            for key, value in dct.items():
                if isinstance(value, str):
                    try:
                        # Attempt to parse as ISO 8601 datetime
                        dct[key] = datetime.datetime.fromisoformat(value)
                    except ValueError:
                        # If it's not a datetime string, keep it as is
                        pass
            return dct

        # Load the JSON string into a dictionary, using the custom object hook
        loaded_data = json.loads(json_string, object_hook=datetime_parser)
        
        # Reconstruct the BacktestConfiguration object from the loaded dictionary
        # using dictionary unpacking (**loaded_data)
        return cls(**loaded_data)
    

    def __repr__(self):
        """Return a nicely formatted output of the BacktestConfiguration."""
        return self.to_json()
        
        
        

