"""Module for tracking and analyzing trading performance metrics and generating statistics."""
import logging
import os
import math
import polars as pl
from mattlibrary.helpers.datetime_helper import format_duration
from mattlibrary.trading.parent_order_base import ParentOrderBase
from mattlibrary.trading.trade import Trade
from mattlibrary.trading.position import Position
from mattlibrary.visualizations.line_scatter_chart import plot_line_scatter_chart
from mattlibrary.visualizations.bar_chart import plot_bar_chart

class PerformanceMetrics():
    """Tracks and analyzes trading performance metrics including PnL, drawdowns, and trade statistics."""

    def __init__(self, starting_balance: int, base_currency: str):
        self.logger = logging.getLogger(__name__)
        self.starting_balance = starting_balance
        self.base_currency = base_currency

        #statistics dataframes
        self.child_orders = None
        self.trades = None
        self.positions = None
        self.data_series = None
        self.weekly_pnl_series = None
        self.monthly_pnl_series = None
        self.annual_pnl_series = None
        self.metrics = None

    
    def generate_statistics(self, total_number_quotes:int, parent_orders:list[ParentOrderBase], trade_list:list[Trade], position_list:list[Position]):
        """Generates trading performance statistics including PnL, drawdowns, and trade statistics."""

        try:

            #abort if any of the passed lists dont contain any data
            if not parent_orders:
                self.logger.error("No parent orders have been recorded.")
                return
            if not trade_list:
                self.logger.error("No trades have been recorded.")
                return
            if not position_list:
                self.logger.error("No positions have been recorded.")
                return
            if not (parent_orders and trade_list and position_list):
                self.logger.error("Either no parent orders, trades, or positions have been recorded.")
                return

            # Convert dataclasses to DataFrames and add to statistics
            self.child_orders = pl.DataFrame([vars(child_order) for parent_order in parent_orders for child_order in parent_order.child_orders.values()])
            self.trades = pl.DataFrame([vars(trade) for trade in trade_list])
            self.positions = pl.DataFrame([vars(pos) for pos in position_list])

            # Generate current_positions by getting the last position for each unique strategy_id and symbol_id combination
            current_positions = (self.positions
                .sort("timestamp_current")
                .group_by(["strategy_id", "symbol_id"])
                .last()
            )

            #data series statistics
            daily_data_series = self.positions.sort("timestamp_current").group_by_dynamic("timestamp_current", every='1d').agg(
                pl.col("unrealized_pnl_base").add(pl.col("realized_pnl_base")).sum().alias("daily_pnl")).select(
                "timestamp_current", pl.col("daily_pnl").diff()).drop_nulls().sort("timestamp_current")
            
            daily_data_series = daily_data_series.with_columns(equity_curve=pl.col("daily_pnl").cum_sum() + self.starting_balance)
            daily_data_series = daily_data_series.with_columns(daily_return=pl.col("daily_pnl") / self.starting_balance)
            daily_data_series = daily_data_series.with_columns(cumulative_return=pl.col("daily_return").cum_sum())
            drawdown_data = self._calculate_drawdown(daily_data_series["equity_curve"].drop_nulls())
            daily_data_series = daily_data_series.with_columns(drawdown = pl.Series(drawdown_data[0]))

            self.data_series = daily_data_series
            self.weekly_pnl_series = daily_data_series.group_by_dynamic("timestamp_current", every='1w', start_by='monday').agg(pl.col("daily_return").sum().alias("weekly_pnl"))
            self.monthly_pnl_series = daily_data_series.group_by_dynamic("timestamp_current", every='1mo').agg(pl.col("daily_return").sum().alias("monthly_pnl"))
            self.annual_pnl_series = daily_data_series.group_by_dynamic("timestamp_current", every='1y').agg(pl.col("daily_return").sum().alias("annual_pnl"))
            
            # Calculate all metrics first as raw values
            starting_balance = self.starting_balance
            
            # Calculate PnL values
            total_pnl = current_positions.select(pl.col("realized_pnl_base").add(pl.col("unrealized_pnl_base")).sum()).item()
            ending_balance = starting_balance + total_pnl
            total_return_pct = total_pnl / starting_balance
            
            # Calculate Sharpe ratio
            daily_return_mean = daily_data_series.select(pl.col("daily_return").mean()).item()
            daily_return_std = daily_data_series.select(pl.col("daily_return").std()).item()
            sharpe_ratio = daily_return_mean / daily_return_std * math.sqrt(252)

            # Compound Annual Growth Rate
            start_date = daily_data_series.select(pl.col("timestamp_current").first()).item()
            end_date = daily_data_series.select(pl.col("timestamp_current").last()).item()
            duration = end_date - start_date
            cagr_years = duration.days / 365.2425
            compound_annual_growth_rate = (ending_balance / starting_balance) ** (1 / cagr_years) - 1

            # Average Annual Return
            average_annual_return = (ending_balance / starting_balance - 1) / cagr_years

            # Counts
            total_quotes = total_number_quotes
            positions_count = len(current_positions)
            child_orders_count = len(self.child_orders)
            total_trades_count = len(self.trades)
            winning_trades_count = len(self.trades.filter(pl.col("pnl_base") >= 0))
            losing_trades_count = len(self.trades.filter(pl.col("pnl_base") < 0))

            # Trade Performance Statistics
            win_rate = winning_trades_count / total_trades_count
            avg_trade_pnl = self.trades.select(pl.col("pnl_base")).mean().item()
            avg_winning_trade_pnl = self.trades.filter(pl.col("pnl_base") >= 0).select(pl.col("pnl_base")).mean().item()
            avg_losing_trade_pnl = self.trades.filter(pl.col("pnl_base") < 0).select(pl.col("pnl_base")).mean().item()
            largest_winning_trade = self.trades.select(pl.col("pnl_base")).max().item()
            largest_losing_trade = self.trades.select(pl.col("pnl_base")).min().item()
            avg_trade_pnl_pct = self.trades.select(pl.col("pnl_local") / (pl.col("price_entry") * abs(pl.col("size")))).mean().item()
            avg_winning_trade_pnl_pct = self.trades.filter(pl.col("pnl_local") >= 0).select(pl.col("pnl_local") / (pl.col("price_entry") * abs(pl.col("size")))).mean().item()
            avg_losing_trade_pnl_pct = self.trades.filter(pl.col("pnl_local") < 0).select(pl.col("pnl_local") / (pl.col("price_entry") * abs(pl.col("size")))).mean().item()
            expectancy_per_trade = win_rate * avg_winning_trade_pnl_pct - (1 - win_rate) * abs(avg_losing_trade_pnl_pct)

            #TODO: r-multiple 

            # Durations
            avg_trade_duration = self.trades.select(pl.col("timestamp_exit").sub(pl.col("timestamp_entry")).mean()).item()
            min_trade_duration = self.trades.select(pl.col("timestamp_exit").sub(pl.col("timestamp_entry")).min()).item()
            max_trade_duration = self.trades.select(pl.col("timestamp_exit").sub(pl.col("timestamp_entry")).max()).item()
            max_drawdown_recovery = drawdown_data[1]
            
            # Create lists for metrics
            metric_names = []
            metric_values = []
            
            # Helper functions for formatting
            def currency_format(value, precision):
                return "$" + format(value, f",.{precision}f")

            def percent_format(value, precision):
                return format(value*100, f",.{precision}f") + " %"
            
            # Profitability section
            metric_names.append("Overall Profitability")
            metric_values.append("Overall Profitability")
            
            metric_names.append("-------------")
            metric_values.append("-------------")
            
            metric_names.append("Starting Balance")
            metric_values.append(currency_format(starting_balance, 0))
            
            metric_names.append("Ending Balance")
            metric_values.append(currency_format(ending_balance, 0))
            
            metric_names.append("Total Return ($)")
            metric_values.append(currency_format(total_pnl, 0))
            
            metric_names.append("Total Return (%)")
            metric_values.append(percent_format(total_return_pct, 2))
            
            metric_names.append("Sharpe Ratio")
            metric_values.append(format(sharpe_ratio, ",.2f"))
            
            metric_names.append("Compound Annual Growth Rate")
            metric_values.append(percent_format(compound_annual_growth_rate, 2))
            
            metric_names.append("Average Annual Return")
            metric_values.append(percent_format(average_annual_return, 2))

            metric_names.append("-------------")
            metric_values.append("-------------")
            
            metric_names.append("Trade Performance")
            metric_values.append("Trade Performance")
            
            metric_names.append("-------------")
            metric_values.append("-------------")

            metric_names.append("Average Trade PnL ($)")
            metric_values.append(currency_format(avg_trade_pnl, 2))
            
            metric_names.append("Average Winning Trade PnL ($)")
            metric_values.append(currency_format(avg_winning_trade_pnl, 2))
            
            metric_names.append("Average Losing Trade PnL ($)")
            metric_values.append(currency_format(avg_losing_trade_pnl, 2))
            
            metric_names.append("Largest Winning Trade")
            metric_values.append(currency_format(largest_winning_trade, 2))
            
            metric_names.append("Largest Losing Trade")
            metric_values.append(currency_format(largest_losing_trade, 2))

            metric_names.append("Average Trade PnL (%)")
            metric_values.append(percent_format(avg_trade_pnl_pct, 5))
        
            metric_names.append("Average Winning Trade PnL (%)")
            metric_values.append(percent_format(avg_winning_trade_pnl_pct, 5))
            
            metric_names.append("Average Losing Trade PnL (%)")
            metric_values.append(percent_format(avg_losing_trade_pnl_pct, 5))

            metric_names.append("Win Rate (%)")
            metric_values.append(percent_format(win_rate, 2))
            
            metric_names.append("Loss Rate (%)")
            metric_values.append(percent_format(1 - win_rate, 2))

            metric_names.append("Expectancy Per Trade (%)")
            metric_values.append(percent_format(expectancy_per_trade, 5))

            metric_names.append("-------------")
            metric_values.append("-------------")
            
            metric_names.append("Counts")
            metric_values.append("Counts")
            
            metric_names.append("-------------")
            metric_values.append("-------------")
            
            metric_names.append("Total Number Quotes")
            metric_values.append(format(total_quotes, ","))
            
            metric_names.append("Number Positions")
            metric_values.append(format(positions_count, ","))
            
            metric_names.append("Number Child Orders")
            metric_values.append(format(child_orders_count, ","))
            
            metric_names.append("Number Trades")
            metric_values.append(format(total_trades_count, ","))

            metric_names.append("Winning Trades")
            metric_values.append(format(winning_trades_count, ","))
            
            metric_names.append("Losing Trades")
            metric_values.append(format(losing_trades_count, ","))
            
            metric_names.append("-------------")
            metric_values.append("-------------")
            
            metric_names.append("Durations")
            metric_values.append("Durations")
            
            metric_names.append("-------------")
            metric_values.append("-------------")
            
            metric_names.append("Starting Date")
            metric_values.append(str(start_date))
            
            metric_names.append("Ending Date")
            metric_values.append(str(end_date))
            
            metric_names.append("Duration")
            metric_values.append(format_duration(duration))
            
            metric_names.append("Average Trade Duration")
            metric_values.append(format_duration(avg_trade_duration))
            
            metric_names.append("Min Trade Duration")
            metric_values.append(format_duration(min_trade_duration))
            
            metric_names.append("Max Trade Duration")
            metric_values.append(format_duration(max_trade_duration))
            
            metric_names.append("Max Drawdown Recovery Period")
            metric_values.append(str(max_drawdown_recovery))
            
            # Convert metrics lists to DataFrame
            self.metrics = pl.DataFrame({
                "Statistics": metric_names,
                "Values": metric_values
            })

            self.logger.info("Performance Statistics successfully generated.")
        
        except Exception as e:
            self.logger.error(f"Error generating Performance Statistics: {e}")
            raise
  

    def export_statistics_to_excel(self, output_dir: str = "."):
        """Exports statistics to separate Excel files.
        
        Args:
            output_dir (str, optional): Directory to save the Excel files. Defaults to current directory.
        """

        try:
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)
            
            # Export data series with percentage formatting for return columns
            self.data_series.write_excel(
                    os.path.join(output_dir, "data_series.xlsx"),
                    column_formats={
                        "daily_return": "0.00%",
                        "cumulative_return": "0.00%",
                        "drawdown": "0.00%"
                    }
                )
            
            # Export PnL series to separate files with percentage formatting
            self.weekly_pnl_series.write_excel(
                os.path.join(output_dir, "weekly_pnl_series.xlsx"),
                column_formats={"weekly_pnl": "0.00%"}
            )
                
            self.monthly_pnl_series.write_excel(
                os.path.join(output_dir, "monthly_pnl_series.xlsx"),
                column_formats={"monthly_pnl": "0.00%"}
            )
                
            self.annual_pnl_series.write_excel(
                os.path.join(output_dir, "annual_pnl_series.xlsx"),
                column_formats={"annual_pnl": "0.00%"}
            )
            
            # Export trades
            self.trades.write_excel(os.path.join(output_dir, "trades.xlsx"))
            
            # Export child orders
            self.child_orders.write_excel(os.path.join(output_dir, "child_orders.xlsx"))
            
            # Export positions
            self.positions.write_excel(os.path.join(output_dir, "positions.xlsx"))
            
            # Export metrics
            self.metrics.write_excel(os.path.join(output_dir, "metrics.xlsx"))

            self.logger.info(f"Performance Statistics successfully exported to Excel files in directory: {output_dir}")

        except Exception as e:
            self.logger.error(f"Error exporting Performance Statistics to Excel: {e}")
            raise

    
    def generate_visuals(self):
        """Generates visualizations for trading performance statistics."""
        
        #equity curve
        plot_line_scatter_chart(
            pl.DataFrame({"timestamp": self.data_series["timestamp_current"], "equity": self.data_series["equity_curve"]}),
            ["equity"], 
            ["blue"], 
            "Equity Curve", 
            "Date", 
            "Value", 
            "0,0", 
            1000, 
            600
        )

        #max drawdowns
        plot_line_scatter_chart(
            pl.DataFrame({"timestamp": self.data_series["timestamp_current"], "drawdown": self.data_series["drawdown"]}),
            ["drawdown"], 
            ["red"], 
            "Drawdown Curve", 
            "Date", 
            "Value", 
            "00.0%", 
            1000, 
            400
        )

        #derive percent return per symbol
        current_positions = (self.positions
                .sort("timestamp_current")
                .group_by(["strategy_id", "symbol_id"])
                .last())
        
        current_positions = current_positions.group_by("symbol_id").agg((pl.col("unrealized_pnl_base").add(pl.col("realized_pnl_base")).sum() / self.starting_balance).alias("pnl"))
        x_data = current_positions["symbol_id"].to_list()
        y_data = current_positions["pnl"].to_list()

        plot_bar_chart(pl.DataFrame({"symbol": x_data, "color": ["lightgreen"] * len(x_data), "pnl": y_data}),
                       "Percent Return by Symbol", 
                       "Percent Return", 
                       "0.00%",
                       1000, 
                       600)


        #Weekly Pnl Barchart
        x_data = self.weekly_pnl_series["timestamp_current"].dt.strftime("%W-%Y").cast(pl.Utf8).to_list()
        y_data = self.weekly_pnl_series["weekly_pnl"]
        
        plot_bar_chart(
            pl.DataFrame({"week": x_data, "color": ["lightgreen"] * len(x_data), "pnl": y_data}),
            "Weekly PnL", 
            "", 
            "0.00 %",
            1000, 
            600
        )

        #Monthly Pnl Barchart
        x_data = self.monthly_pnl_series["timestamp_current"].dt.strftime("%B-%Y").cast(pl.Utf8).to_list()
        y_data = self.monthly_pnl_series["monthly_pnl"]
        plot_bar_chart(
            pl.DataFrame({"month": x_data, "color": ["lightgreen"] * len(x_data), "pnl": y_data}),
            "Monthly PnL", 
            "", 
            "0.00 %",
            1000, 
            600
        )

        #Annual Pnl Barchart
        x_data = self.annual_pnl_series["timestamp_current"].dt.strftime("%Y").cast(pl.Utf8).to_list()
        y_data = self.annual_pnl_series["annual_pnl"]
        plot_bar_chart(
            pl.DataFrame({"year": x_data, "color": ["lightgreen"] * len(x_data), "pnl": y_data}),
            "Annual PnL", 
            "", 
            "0.00 %",
            1000, 
            600
        )

        self.logger.info("Visualizations successfully generated.")


    def _calculate_drawdown(self, data_series:pl.Series) -> tuple[list, int]:
            """Calculates the drawdown of the equity curve."""
            drawdowns = []
            peak_price = -math.inf
            drawdown_recovery_period = 0

            for pnl in data_series:
                if pnl > peak_price:
                    peak_price = pnl
                    drawdown_recovery_period = 0
                else:
                    drawdown_recovery_period += 1

                drawdown = min(0.0, (pnl - peak_price) / peak_price)
                drawdowns.append(drawdown)

            return (drawdowns, drawdown_recovery_period)
