from typing import List
from mattlibrary.trading.strategy_base import StrategyBase
from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration
from mattlibrary.trading.trading_strategies.sma_cross_over.sma_cross_over import SmaCrossoverStrategy


def generate_strategies(configuration: BacktestConfiguration) -> List[StrategyBase]:
    strategies = []
    for symbol in configuration.symbols_traded:
        strategy = SmaCrossoverStrategy(
            symbol, 
            configuration.sma_window_size, 
            configuration.order_size, 
            configuration.is_target_size, 
            configuration.enable_strategy_logging)
        strategies.append(strategy)
    return strategies