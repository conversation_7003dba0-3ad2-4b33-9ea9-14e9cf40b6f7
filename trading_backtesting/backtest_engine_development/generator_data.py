import polars as pl
from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager
from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration


def generate_data(configuration: BacktestConfiguration) -> pl.DataFrame:
    manager = PolarsDataFrameManager(base_directory=str(configuration.data_directory))
    filter_expr = pl.col("symbol").is_in(configuration.symbols_data) & (pl.col("datetime").is_between(configuration.start_dt, configuration.end_dt))
    df = manager.read_data(dataset_name=configuration.dataset_name, filter_expr=filter_expr)
    source_data = df.sort("symbol", "datetime").with_columns(sma=pl.col("close").rolling_mean(window_size=configuration.sma_window_size).over("symbol")).drop_nulls().sort("datetime")
    return source_data