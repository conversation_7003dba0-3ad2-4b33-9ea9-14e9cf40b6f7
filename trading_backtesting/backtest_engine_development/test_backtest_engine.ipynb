{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import datetime\n", "import mattlibrary.backtesting.backtest_engine as backtest_engine\n", "from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration\n", "from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager\n", "from mattlibrary.trading.performance_metrics import PerformanceMetrics\n", "from mattlibrary.backtesting.backtest_storage_manager import BacktestStorageManager"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#backtest parameters\n", "configuration = BacktestConfiguration(\n", "    starting_balance=100000,\n", "    base_currency=\"USD\",\n", "    symbols_data=['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY', 'EURAUD', 'GBPAUD',\n", "                'NZDUSD', 'NZDCAD', 'NZDCHF', 'NZDJPY', 'EURNZD', 'GBPNZD', \n", "                'CADCHF', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',\n", "                'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',\n", "                'EURJPY', 'EURUSD', 'EURGBP', \n", "                'GBPJPY', 'GBPUSD',\n", "                'USDJPY'],\n", "\n", "    symbols_traded=['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY', 'EURAUD', 'GBPAUD',\n", "                'NZDUSD', 'NZDCAD', 'NZDCHF', 'NZDJPY', 'EURNZD', 'GBPNZD', \n", "                'CADCHF', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',\n", "                'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',\n", "                'EURJPY', 'EURUSD', 'EURGBP', \n", "                'GBPJPY', 'GBPUSD',\n", "                'USDJPY'],\n", "    \n", "    # Data parameters\n", "    data_directory = \"/mnt/sambashare/nas/storage/Share2_FinancialData1/Dukascopy/Currencies/dukascopy_fx_polars_db/\",\n", "    dataset_name=\"dukascopy_daily_fx\",\n", "    start_dt=datetime.datetime(2000, 5, 3),\n", "    end_dt=datetime.datetime(2023, 12, 31),\n", "    \n", "    # Strategy parameters\n", "    sma_window_size=30,\n", "    order_size=100_000,\n", "    is_target_size=False,\n", "    execution_plugin_type=\"SimulatedExecutionEngine\",\n", "    enable_strategy_logging=False,\n", "    \n", "    # Logging\n", "    logging_enabled=False,\n", "    log_to_console=False,\n", "    log_to_file=False,\n", "    log_file=os.path.join(\"logs\", \"backtest.log\"),\n", "    \n", "    # Performance tracking\n", "    track_performance=True,\n", "    excel_logging=False,\n", "    excel_output_dir=\"excel\",\n", "    visualize_performance=False\n", ")\n", "\n", "#configure strategies\n", "from trading_backtesting.backtest_engine_development.generator_strategies import generate_strategies\n", "strategies = generate_strategies(configuration)\n", "\n", "#configure data source\n", "from trading_backtesting.backtest_engine_development.generator_data import generate_data\n", "source_data = generate_data(configuration)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["#backtest meta data storage manager\n", "backtest_base_directory = \"/home/<USER>/development/python_development/backtests/\"\n", "backtest_storage_manager = BacktestStorageManager(backtest_base_directory)\n", "\n", "#iterate the below code 10 times\n", "for i in range(10):\n", "\n", "    #create new backtest metata\n", "    backtest_storage_manager.store_backtest_metadata(configuration, strategies, generate_strategies, generate_data)\n", "\n", "    #initialize backtest engine\n", "    engine = backtest_engine.BacktestEngine(configuration, strategies, source_data)\n", "\n", "    #start backtest\n", "    engine.start_backtest()\n", "\n", "    #finalize backtest\n", "    engine.finalize()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["#load backtest metadata\n", "backtest_path = os.path.join(backtest_base_directory, \"backtest_2025-May-29 16-34-49\")\n", "configuration, strategies, source_data = backtest_storage_manager.load_backtest_modules(backtest_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if configuration.track_performance:\n", "    #obtain performance data (dictionary)\n", "    performance_data = engine.get_performance_data()\n", "\n", "    #generate statistics\n", "    performance_metrics = PerformanceMetrics(configuration.starting_balance, configuration.base_currency)\n", "    performance_metrics.generate_statistics(performance_data[\"data_point_count\"], \n", "                                            performance_data[\"parent_orders\"], \n", "                                            performance_data[\"trades\"], \n", "                                            performance_data[\"position_records\"])\n", "\n", "    #export data to excel\n", "    if configuration.excel_logging:\n", "        performance_metrics.export_statistics_to_excel(output_dir=configuration.excel_output_dir)\n", "\n", "    #output statistics\n", "    with pl.Config(fmt_str_lengths=100, tbl_rows=500):\n", "        print(performance_metrics.metrics)\n", "\n", "    #generate visuals\n", "    if configuration.visualize_performance:\n", "        performance_metrics.generate_visuals()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}